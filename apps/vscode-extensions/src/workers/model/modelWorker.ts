import { parentPort, workerData } from 'worker_threads';
import { createStructuredPrompt } from '../../utils/promptTemplates';

interface WorkerData {
  prompt: string;
  searchResults: Array<{
    text: string;
    score: number;
    source: string;
  }>;
  modelId?: string;
  chatHistory?: string;
}

interface OllamaResponse {
  response: string;
  done: boolean;
}

const { prompt, searchResults, modelId = 'llama2', chatHistory = '' } = workerData as WorkerData;

async function generateResponse(): Promise<void> {
  try {
    const structuredPrompt = createStructuredPrompt(searchResults, prompt, chatHistory);

    const response = await fetch('http://localhost:11434/api/generate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: modelId,
        prompt: structuredPrompt,
        stream: false,
        options: {
          temperature: 0.3,
          num_predict: 512,
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Generation failed: ${response.statusText}`);
    }

    const result = await response.json() as OllamaResponse;

    if (!result.response) {
      throw new Error('No text was generated by the model');
    }

    parentPort?.postMessage({
      type: 'response',
      content: result.response
    });
  } catch (error) {
    parentPort?.postMessage({
      type: 'error',
      message: error instanceof Error ? error.message : String(error),
    });
  }
}

// Start processing
generateResponse();
