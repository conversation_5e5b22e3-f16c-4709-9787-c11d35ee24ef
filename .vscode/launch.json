{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        
        {
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}/apps/vscode-extensions",
                "--disable-extensions"
            ],
            "name": "workspacegpt-extension",
            "outFiles": [
                "${workspaceFolder}/apps/vscode-extensions/**/*.js"
            ],
            "request": "launch",
            "preLaunchTask": "npm: build - apps/vscode-extensions",
            "type": "extensionHost"
        },
        {
            "name": "confluence-rag",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/apps/confluence-rag/src/main.py",
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env"
        },
        {
            "name": "confluence-rag-api",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/apps/confluence-rag/src/api.py",
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env"
        },
        {
            "request": "launch",
            "name": "confluence-rag-streamlit",
            "type": "debugpy",
            "module": "streamlit",
            "args": "run ${workspaceFolder}/apps/confluence-rag/src/main.py",
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "confluence-extractor",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "cwd": "${workspaceFolder}/apps/confluence-extractor",
            "program": "${workspaceFolder}/apps/confluence-extractor/dist/index.js",
            "preLaunchTask": "build:all",
            "outFiles": ["${workspaceFolder}/apps/confluence-extractor/dist/**/*.js"],
            "sourceMaps": true,
            "envFile": "${workspaceFolder}/.env",
            "resolveSourceMapLocations": [
                "${workspaceFolder}/apps/confluence-extractor/dist/**/*.js",
                "${workspaceFolder}/packages/confluence-utils/dist/**/*.js"
            ]
        }
    ]
}
